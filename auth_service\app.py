"""
Main application file for the Auth Service.

This module initializes and configures the Flask application for the Auth Service.

English: This file sets up the Flask app for the Auth Service
Tanglish: Indha file Auth Service-kku Flask app-a setup pannum
"""

import os
from flask import Flask
from flask_cors import CORS
from auth_service.common.db_config import init_db
from auth_service.common.middleware import JWTMiddleware
from auth_service.common.logger import setup_logger
from auth_service.views import auth_bp
from auth_service.config import get_config

def create_app():
    """
    Create and configure the Flask application.

    Returns:
        Configured Flask application

    English: This function creates and configures the Flask app
    Tanglish: Indha function Flask app-a create panni configure pannum
    """
    print("Creating Auth Service Flask app...")

    # Create Flask app
    app = Flask(__name__)
    print("Flask app created")

    # Load configuration
    app.config.from_object(get_config())
    print("Configuration loaded")

    # Enable CORS with specific configuration
    CORS(app,
         resources={r"/*": {"origins": ["http://localhost:5173", "http://localhost:5174"]}},
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept",
                       "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"],
         supports_credentials=True,
         expose_headers=["Content-Type", "Authorization"],
         max_age=3600)
    print("CORS enabled with specific configuration")

    try:
        # Initialize database
        print("Initializing database...")
        init_db(app, 'auth')
        print("Database initialized")

        # Register blueprints
        app.register_blueprint(auth_bp, url_prefix='/api/auth')
        print("Blueprints registered")

        # Set up logger
        logger = setup_logger('auth_service')
        print("Logger set up")

        # Apply middleware
        # The login route is exempt from JWT verification
        app.wsgi_app = JWTMiddleware(
            app.wsgi_app,
            exempt_routes=['/api/auth/login', '/api/auth/health']
        )
        print("Middleware applied")

        # We don't need this anymore as Flask-CORS is already handling CORS
        # @app.after_request
        # def add_cors_headers(response):
        #     response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        #     response.headers.add('Access-Control-Allow-Credentials', 'true')
        #     response.headers.add('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        #     response.headers.add('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        #     return response

        print("Auth Service app creation completed successfully")
        return app
    except Exception as e:
        print(f"Error creating Auth Service app: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('AUTH_SERVICE_PORT', 5000))

    # Create and run the app
    app = create_app()
    print("Auth Service app created, starting server...")
    app.run(host='0.0.0.0', port=port, use_reloader=False)
