"""
Main application file for the Student Service.

This module initializes and configures the Flask application for the Student Service.

English: This file sets up the Flask app for the Student Service
Tanglish: Indha file Student Service-kku Flask app-a setup pannum
"""

import os
from flask import Flask, request
from flask_cors import CORS
from student_service.common.db_config import init_db
from student_service.common.middleware import JWTMiddleware
from student_service.common.logger import setup_logger
from student_service.views import student_bp
from student_service.config import get_config

def create_app():
    """
    Create and configure the Flask application.

    Returns:
        Configured Flask application

    English: This function creates and configures the Flask app
    Tanglish: Indha function Flask app-a create panni configure pannum
    """
    # Create Flask app
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(get_config())

    # Enable CORS with specific configuration
    CORS(app,
         origins=["http://localhost:5173", "http://localhost:5174"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With", "Accept",
                       "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"],
         supports_credentials=True,
         expose_headers=["Content-Type", "Authorization"],
         max_age=3600)

    # Initialize database
    init_db(app, 'student')

    # Register blueprints
    app.register_blueprint(student_bp, url_prefix='/api/students')

    # Set up logger
    logger = setup_logger('student_service')

    # Add middleware to pass service URLs to request environment
    @app.before_request
    def add_service_urls():
        """
        Add service URLs to request environment.

        English: This function adds service URLs to the request environment
        Tanglish: Indha function service URLs-a request environment-la add pannum
        """
        request.environ['PARENT_SERVICE_URL'] = app.config.get('PARENT_SERVICE_URL')

    # Apply middleware
    # Define role-based access control
    required_roles = {
        '/api/students/students': ['Super Admin', 'Admin', 'Teacher', 'Student', 'Parent'],  # Allow all authenticated users
        '/api/students/map-parent': ['Super Admin', 'Admin', 'Teacher'],
        '/api/students/student-profile': ['Student']  # Only students can access their profile
    }

    # Apply middleware
    app.wsgi_app = JWTMiddleware(
        app.wsgi_app,
        exempt_routes=['/api/students/health'],
        required_roles=required_roles
    )

    return app

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('STUDENT_SERVICE_PORT', 5002))

    # Create and run the app
    app = create_app()
    print("Student Service app created, starting server...")
    app.run(host='0.0.0.0', port=port, use_reloader=False)
