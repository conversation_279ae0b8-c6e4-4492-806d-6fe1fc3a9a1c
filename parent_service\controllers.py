"""
Controllers for the Parent Service.

This module contains the business logic for parent management.

English: This file contains the logic for parent management
Tanglish: Indha file-la parent management-kku logic irukku
"""

from flask import jsonify, request
from parent_service.common.utils import handle_error, validate_request_data, call_service
from parent_service.models import Parent
from parent_service.common.db_config import db

def register_parent():
    """
    Register a new parent.

    Returns:
        JSON response with the created parent

    English: This function registers a new parent
    Tanglish: Indha function puthusa oru parent-a register pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['user_id', 'first_name', 'last_name']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')

    # Check if the user has permission to register a parent
    if user_role not in ['Super Admin', 'Admin', 'Teacher']:
        return handle_error("You don't have permission to register a parent", 403)

    # Check if parent with this user_id already exists
    try:
        # Use a more specific query to avoid the main_code column
        existing_parent = db.session.query(
            Parent.id, Parent.user_id, Parent.first_name, Parent.last_name
        ).filter_by(user_id=data['user_id']).first()

        if existing_parent:
            return handle_error("Parent with this user_id already exists", 400)
    except Exception as e:
        print(f"Error checking for existing parent: {str(e)}")
        # Continue with registration even if check fails

    # Create new parent
    parent = Parent(
        user_id=data['user_id'],
        first_name=data['first_name'],
        last_name=data['last_name'],
        occupation=data.get('occupation'),
        address=data.get('address'),
        phone=data.get('phone')
    )

    # Save parent to database
    db.session.add(parent)
    db.session.commit()

    # Return parent information
    return jsonify({
        "message": "Parent registered successfully",
        "parent": parent.to_dict()
    }), 201

def get_parents():
    """
    Get all parents.

    Returns:
        JSON response with all parents

    English: This function gets all parents
    Tanglish: Indha function ella parents-um get pannum
    """
    # Get user role from request environment
    user_role = request.environ.get('user_role')

    # Check if the user has permission to view all parents
    if user_role not in ['Super Admin', 'Admin', 'Teacher']:
        return handle_error("You don't have permission to view all parents", 403)

    # Get all parents
    parents = Parent.query.all()

    # Return parents
    return jsonify({
        "parents": [parent.to_dict() for parent in parents]
    })

def get_parent(parent_id):
    """
    Get a specific parent.

    Args:
        parent_id: ID of the parent to get

    Returns:
        JSON response with the parent

    English: This function gets a specific parent
    Tanglish: Indha function specific parent-a get pannum
    """
    # Get the parent
    parent = Parent.query.get(parent_id)
    if not parent:
        return handle_error("Parent not found", 404)

    # Get user role and ID from request environment
    user_role = request.environ.get('user_role')
    user_id = request.environ.get('user_id')

    # Check if the user has permission to view this parent
    # Super Admin, Admin, and Teacher can view any parent
    # Parents can only view themselves
    if user_role not in ['Super Admin', 'Admin', 'Teacher']:
        if user_role == 'Parent' and str(parent.user_id) != str(user_id):
            return handle_error("You don't have permission to view this parent", 403)

    # Return parent
    return jsonify({
        "parent": parent.to_dict()
    })

def get_parent_by_user_id(user_id):
    """
    Get a parent by user ID.

    Args:
        user_id: ID of the user

    Returns:
        JSON response with the parent

    English: This function gets a parent by user ID
    Tanglish: Indha function user ID moolama parent-a get pannum
    """
    # Get the parent
    parent = Parent.query.filter_by(user_id=user_id).first()
    if not parent:
        return handle_error("Parent not found", 404)

    # Get user role and ID from request environment
    user_role = request.environ.get('user_role')
    request_user_id = request.environ.get('user_id')

    # Allow access to parent data for all roles
    # This is needed for parent-student mapping and parent dashboard

    # Return parent
    return jsonify({
        "parent": parent.to_dict()
    })
