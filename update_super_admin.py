"""
<PERSON><PERSON><PERSON> to update the existing super admin user with main_code.

This script updates the existing super admin user to set the main_code value.
"""

import os
import sys
import random
import string
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection(db_name):
    """Get a database connection."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')

    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )

    return conn

def generate_school_code():
    """
    Generate a unique school code in the format of 6 characters (3 letters followed by 3 numbers).

    Returns:
        A string containing 3 uppercase letters followed by 3 numbers
    """
    # Generate 3 random uppercase letters
    letters = ''.join(random.choices(string.ascii_uppercase, k=3))

    # Generate 3 random numbers
    numbers = ''.join(random.choices(string.digits, k=3))

    # Combine letters and numbers to form the school code
    school_code = f"{letters}{numbers}"

    return school_code

def update_super_admin(db_name, username, main_code):
    """Update the super admin user with main_code."""
    try:
        conn = get_db_connection(db_name)
        cursor = conn.cursor()

        # Update the super admin user
        cursor.execute("""
            UPDATE users
            SET main_code = %s
            WHERE username = %s AND role = 'Super Admin'
        """, (main_code, username))

        rows_updated = cursor.rowcount
        conn.commit()

        if rows_updated > 0:
            print(f"Updated super admin user '{username}' in {db_name} with main_code '{main_code}'")
            return True
        else:
            print(f"No super admin user '{username}' found in {db_name}")
            return False

    except Exception as e:
        print(f"Error updating super admin in {db_name}: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

def main():
    """Main function."""
    username = 'superadmin'
    main_code = generate_school_code()

    # Update super admin in auth_db
    auth_success = update_super_admin('auth_db', username, main_code)

    # Update super admin in user_db
    user_success = update_super_admin('user_db', username, main_code)

    if auth_success and user_success:
        print(f"Successfully updated super admin '{username}' in both databases!")
        return 0
    elif auth_success or user_success:
        print(f"Partially updated super admin '{username}'. Please check the logs.")
        return 1
    else:
        print(f"Failed to update super admin '{username}' in both databases.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
