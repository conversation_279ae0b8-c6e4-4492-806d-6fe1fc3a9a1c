# School Management System Frontend

This is the frontend for the School Management System, built with React and Vite.

## Features

- JWT authentication
- Role-based dashboards
- User management
- Course management
- Student management
- Parent management

## Tech Stack

- React.js
- Vite
- React Router
- Tailwind CSS
- Axios

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm 7.x or higher

### Installation

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm run dev
```

The application will be available at http://localhost:5173

## Project Structure

```
src/
├── components/       # Reusable UI components
├── contexts/         # React contexts
├── pages/            # Page components
├── services/         # API service functions
├── App.jsx           # Main application component
├── index.css         # Global styles with Tailwind
└── main.jsx          # Application entry point
```

## User Roles

1. **Super Admin**: Can register Admins and Teachers, manage courses
2. **Admin**: Can register Teachers
3. **Teacher**: Can register Students, map them to Courses, and map Parents to Students
4. **Student**: Can view their courses
5. **Parent**: Can view their children's details and courses

## API Integration

The frontend communicates with the following microservices:

- Auth Service (http://localhost:5000)
- User Service (http://localhost:5001)
- Student Service (http://localhost:5002)
- Course Service (http://localhost:5003)
- Parent Service (http://localhost:5004)

## Building for Production

To build the application for production:

```bash
npm run build
```

The build output will be in the `dist` directory.
