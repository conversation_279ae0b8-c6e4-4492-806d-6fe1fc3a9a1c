"""
<PERSON><PERSON><PERSON> to update the student database schema.

This script adds the main_code column to the students table.
"""

import os
import sys
import psycopg2
from psycopg2 import sql

def get_db_connection(db_name):
    """Get a connection to the specified database."""
    # Database connection parameters
    db_params = {
        'dbname': db_name,
        'user': 'postgres',
        'password': 'postgres',
        'host': 'localhost',
        'port': '5432'
    }

    # Connect to the database
    try:
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        return conn
    except Exception as e:
        print(f"Error connecting to {db_name}: {e}")
        return None

def add_main_code_column():
    """Add main_code column to students table."""
    conn = get_db_connection('student_db')
    if not conn:
        return False

    cursor = conn.cursor()
    try:
        # Check if the column already exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='students' AND column_name='main_code'
        """)
        
        if cursor.fetchone():
            print("main_code column already exists in students table.")
            return True

        # Add the main_code column
        cursor.execute("""
            ALTER TABLE students
            ADD COLUMN main_code VARCHAR(50)
        """)
        
        print("main_code column added to students table successfully.")
        return True
    except Exception as e:
        print(f"Error adding main_code column: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == '__main__':
    success = add_main_code_column()
    sys.exit(0 if success else 1)
