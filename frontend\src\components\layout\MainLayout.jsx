/**
 * Main Layout Component
 *
 * This component provides the main layout structure for authenticated pages.
 * It includes the navbar, sidebar, and main content area.
 */

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from './Navbar';

const MainLayout = ({ children, title }) => {
  const { currentUser } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  
  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Navbar */}
      <Navbar />
      
      <div className="flex flex-1">
        {/* Sidebar */}
        <aside
          className={`bg-gray-800 text-white w-64 flex-shrink-0 transition-all duration-300 ease-in-out ${
            isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
          } md:translate-x-0 fixed md:static h-full z-10`}
        >
          <div className="p-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Dashboard</h2>
              <button
                onClick={toggleSidebar}
                className="md:hidden text-white focus:outline-none"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            
            <div className="mt-8">
              <nav className="space-y-2">
                <a
                  href="/dashboard"
                  className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                >
                  Dashboard
                </a>
                
                {/* Conditional navigation based on user role */}
                {currentUser && currentUser.role === 'Super Admin' && (
                  <>
                    <a
                      href="/users"
                      className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                    >
                      User Management
                    </a>
                    <a
                      href="/courses"
                      className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                    >
                      Course Management
                    </a>
                  </>
                )}
                
                {currentUser && currentUser.role === 'Admin' && (
                  <a
                    href="/teachers"
                    className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                  >
                    Teacher Management
                  </a>
                )}
                
                {currentUser && currentUser.role === 'Teacher' && (
                  <>
                    <a
                      href="/students"
                      className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                    >
                      Student Management
                    </a>
                    <a
                      href="/parents"
                      className="block px-4 py-2 rounded-md hover:bg-gray-700 transition-colors duration-200"
                    >
                      Parent Management
                    </a>
                  </>
                )}
              </nav>
            </div>
          </div>
        </aside>
        
        {/* Main content */}
        <main className="flex-1 p-6">
          <div className="container mx-auto">
            {/* Toggle sidebar button (mobile only) */}
            <button
              onClick={toggleSidebar}
              className="md:hidden mb-4 p-2 rounded-md bg-gray-200 hover:bg-gray-300 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
            
            {/* Page title */}
            {title && (
              <h1 className="text-2xl font-bold mb-6">{title}</h1>
            )}
            
            {/* Page content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
