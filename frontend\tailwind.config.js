/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
  safelist: [
    // Core utility classes
    'px-4', 'py-2', 'p-4', 'p-6', 'p-8',
    'mb-1', 'mb-4', 'mt-2', 'mt-8',
    'w-full', 'max-w-md', 'min-h-screen',
    'rounded', 'rounded-md', 'rounded-lg',
    'border', 'border-gray-300', 'border-red-200', 'border-green-200',
    'shadow', 'shadow-md',
    'space-y-6', 'space-y-8',

    // Text utilities
    'text-center', 'text-sm', 'text-2xl',
    'font-medium', 'font-bold',
    'text-white', 'text-gray-600', 'text-gray-700', 'text-gray-800', 'text-gray-900',
    'text-red-700', 'text-green-700',

    // Background utilities
    'bg-white', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300',
    'bg-blue-600', 'bg-blue-700',
    'bg-red-100', 'bg-green-100',

    // Flex utilities
    'flex', 'items-center', 'justify-center', 'block',

    // Interactive utilities
    'hover:bg-blue-700', 'hover:bg-gray-300',
    'focus:outline-none', 'focus:ring-2', 'focus:ring-blue-500',
    'transition-colors',

    // Custom component classes
    'btn', 'btn-primary', 'btn-secondary',
    'card', 'form-input', 'form-label', 'form-group',
    'alert', 'alert-danger', 'alert-success'
  ]
}
