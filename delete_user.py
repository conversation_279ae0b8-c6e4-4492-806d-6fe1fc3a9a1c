"""
Script to delete a user from auth_db and user_db.

This script deletes a user from both the auth_db and user_db databases.
"""

import os
import sys
import argparse
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection(db_name):
    """Get a database connection."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    
    return conn

def delete_user(db_name, username):
    """Delete a user from the specified database."""
    try:
        conn = get_db_connection(db_name)
        cursor = conn.cursor()
        
        # Delete the user
        cursor.execute("""
            DELETE FROM users
            WHERE username = %s
            RETURNING id
        """, (username,))
        
        result = cursor.fetchone()
        conn.commit()
        
        if result:
            print(f"User '{username}' deleted from {db_name} with ID {result[0]}")
            return True
        else:
            print(f"No user '{username}' found in {db_name}")
            return False
        
    except Exception as e:
        print(f"Error deleting user from {db_name}: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Delete a user.')
    parser.add_argument('--username', required=True, help='Username to delete')
    
    args = parser.parse_args()
    
    # Delete user from auth_db
    auth_success = delete_user('auth_db', args.username)
    
    # Delete user from user_db
    user_success = delete_user('user_db', args.username)
    
    if auth_success and user_success:
        print(f"User '{args.username}' deleted successfully from both databases!")
        return 0
    elif auth_success or user_success:
        print(f"User '{args.username}' deleted from one database but not the other.")
        return 1
    else:
        print(f"Failed to delete user '{args.username}' from both databases.")
        return 1

if __name__ == '__main__':
    main()
