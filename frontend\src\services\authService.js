/**
 * Authentication Service
 *
 * This service handles user authentication with the Auth Service.
 *
 * English: This file handles login and token management
 * Tanglish: Indha file login and token management-a handle pannum
 */

import axios from 'axios';
import config from '@/config';

const API_URL = config.API.AUTH;

const authService = {
  /**
   * Login a user
   *
   * @param {string} username - User's username
   * @param {string} password - User's password
   * @returns {Promise} - Promise with login response
   *
   * English: This function logs in a user with username and password
   * Tanglish: Indha function username and password vachi user-a login pannum
   */
  login: async (username, password, schoolCode = null) => {
    try {
      // Create request data
      const requestData = {
        username,
        password
      };

      // For teacher and admin login, include main_code if provided
      if (schoolCode) {
        requestData.main_code = schoolCode;
      }

      const response = await axios.post(`${API_URL}/login`, requestData, {
        withCredentials: true
      });

      // Store token and user info in localStorage
      if (response.data.token) {
        // Verify the token has the required fields before storing
        try {
          const base64Url = response.data.token.split('.')[1];
          const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));

          const payload = JSON.parse(jsonPayload);
          console.log('Token payload:', payload);

          // Debug the role specifically
          console.log('User role from token:', payload.role);

          if (!payload.role) {
            console.error('Token does not contain role information');
            throw new Error('Invalid token format: missing role');
          }

          // Process user data for teacher and admin
          const userData = response.data.user;
          if (userData && (userData.role === 'Teacher' || userData.role === 'Admin')) {
            // If schoolcode exists in the response but main_code doesn't, use schoolcode value
            if (userData.schoolcode && !userData.main_code) {
              userData.main_code = userData.schoolcode;
              delete userData.schoolcode; // Remove the old property
            }

            // Store main_code in localStorage for teacher and admin
            if (userData.main_code) {
              localStorage.setItem(config.AUTH.SCHOOL_CODE_KEY, userData.main_code);
              console.log(`Stored main_code in localStorage: ${userData.main_code}`);
            }
          }

          // Store token and user info if valid
          localStorage.setItem(config.AUTH.TOKEN_KEY, response.data.token);
          localStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(userData || response.data.user));
        } catch (e) {
          console.error('Error decoding token:', e);
          throw { error: 'Invalid token format. Please contact support.' };
        }
      }

      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: error.error || 'Network error' };
    }
  },

  /**
   * Logout a user
   *
   * English: This function logs out a user by removing token and user info
   * Tanglish: Indha function token and user info-a remove panni user-a logout pannum
   */
  logout: () => {
    localStorage.removeItem(config.AUTH.TOKEN_KEY);
    localStorage.removeItem(config.AUTH.USER_KEY);
    localStorage.removeItem(config.AUTH.SCHOOL_CODE_KEY); // Also remove school code
  },

  /**
   * Get the current user
   *
   * @returns {Object|null} - User object or null if not logged in
   *
   * English: This function gets the current logged in user
   * Tanglish: Indha function current logged in user-a get pannum
   */
  getCurrentUser: () => {
    const user = localStorage.getItem(config.AUTH.USER_KEY);
    if (!user) return null;

    const userData = JSON.parse(user);

    // Ensure we're using main_code instead of schoolcode for teacher and admin
    if (userData && (userData.role === 'Teacher' || userData.role === 'Admin')) {
      // If schoolcode exists but main_code doesn't, use schoolcode value
      if (userData.schoolcode && !userData.main_code) {
        userData.main_code = userData.schoolcode;
        delete userData.schoolcode; // Remove the old property

        // Update the stored user data
        localStorage.setItem(config.AUTH.USER_KEY, JSON.stringify(userData));
      }
    }

    return userData;
  },

  /**
   * Get the authentication token
   *
   * @returns {string|null} - Token or null if not logged in
   *
   * English: This function gets the authentication token
   * Tanglish: Indha function authentication token-a get pannum
   */
  getToken: () => {
    return localStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  /**
   * Check if user is logged in
   *
   * @returns {boolean} - True if logged in, false otherwise
   *
   * English: This function checks if a user is logged in
   * Tanglish: Indha function user login aana irukkaara nu check pannum
   */
  isLoggedIn: () => {
    return !!localStorage.getItem(config.AUTH.TOKEN_KEY);
  },

  /**
   * Get authorization header
   *
   * @returns {Object} - Header object with Authorization
   *
   * English: This function gets the authorization header for API requests
   * Tanglish: Indha function API requests-kku authorization header-a get pannum
   */
  getAuthHeader: () => {
    const token = authService.getToken();
    if (token) {
      console.log('Adding Authorization header with token');
      return { Authorization: `Bearer ${token}` };
    } else {
      console.warn('No token available for Authorization header');
      return {};
    }
  },

  /**
   * Verify the current token
   *
   * @returns {Promise} - Promise with verification response
   *
   * English: This function verifies if the current token is valid
   * Tanglish: Indha function current token valid-a irukka nu verify pannum
   */
  verifyToken: async () => {
    try {
      const token = authService.getToken();
      if (!token) {
        return { valid: false, error: 'No token found' };
      }

      // Decode the token locally to check if it has the required fields
      // This is a simple check, not a full verification
      try {
        const base64Url = token.split('.')[1];
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
        const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
        }).join(''));

        const payload = JSON.parse(jsonPayload);
        if (!payload.role) {
          console.warn('Token does not contain role information');
          // Force re-login
          authService.logout();
          return { valid: false, error: 'Invalid token format: missing role' };
        }
      } catch (e) {
        console.error('Error decoding token:', e);
        // Force re-login
        authService.logout();
        return { valid: false, error: 'Invalid token format' };
      }

      return { valid: true };
    } catch (error) {
      console.error('Token verification error:', error);
      return { valid: false, error: error.message };
    }
  },

  /**
   * Get the school code for the current user
   *
   * @returns {string|null} - School code or null if not available
   *
   * English: This function gets the school code for the current user
   * Tanglish: Indha function current user-kku school code-a get pannum
   */
  getSchoolCode: () => {
    // First try to get main_code from localStorage
    const storedMainCode = localStorage.getItem(config.AUTH.SCHOOL_CODE_KEY);
    if (storedMainCode) {
      return storedMainCode;
    }

    // If not found in localStorage, try to get it from user object
    const user = authService.getCurrentUser();
    if (!user) return null;

    // Always return main_code, even if code is looking for schoolcode
    return user.main_code || null;
  }
};

export default authService;
