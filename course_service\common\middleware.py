"""
Middleware for JWT verification and role-based access control.

This module provides middleware functions for verifying JWT tokens and checking user roles
for access control across all microservices.

English: This file contains middleware to check JWT tokens and user roles
Tanglish: Indha file-la JWT token check panradhukkum, user role check panradhukkum middleware irukku
"""

import json
import jwt
import logging
from functools import wraps
from flask import request, jsonify
import os
from werkzeug.wrappers import Request, Response
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class JWTMiddleware:
    """
    WSGI middleware for JWT verification and role-based access control.

    English: This middleware checks if the JWT token is valid and if the user has the right role
    Tanglish: Indha middleware JWT token valid-a irukka nu check pannum, user-kku correct role irukka nu paakkum
    """

    def __init__(self, app, exempt_routes=None, required_roles=None):
        """
        Initialize the middleware.

        Args:
            app: The Flask application
            exempt_routes: List of routes that don't require authentication
            required_roles: Dictionary mapping routes to required roles

        English: This function sets up the middleware with the app and routes that don't need authentication
        Tanglish: Indha function middleware-a setup pannum, authentication thevai illatha routes-um set pannum
        """
        self.app = app
        self.exempt_routes = exempt_routes or []
        self.required_roles = required_roles or {}
        self.jwt_secret = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

    def __call__(self, environ, start_response):
        # Debug JWT verification
        path = environ.get('PATH_INFO', '')
        print(f"Processing request to: {path}")

        """
        WSGI callable to process each request.

        English: This function runs for every request to check the JWT token and user role
        Tanglish: Indha function ovvoru request-kkum odi JWT token-um user role-um check pannum
        """
        request = Request(environ)
        path = request.path

        # Skip middleware for exempt routes and OPTIONS requests
        if any(path.startswith(route) for route in self.exempt_routes) or request.method == 'OPTIONS':
            # For OPTIONS requests, add CORS headers
            if request.method == 'OPTIONS':
                response = Response(
                    '',
                    status=200,
                    headers={
                        'Access-Control-Allow-Origin': 'http://localhost:5173',
                        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
                        'Access-Control-Allow-Credentials': 'true',
                        'Access-Control-Max-Age': '3600',
                    }
                )
                return response(environ, start_response)
            return self.app(environ, start_response)

        # Check for JWT token
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning(f"Missing or invalid Authorization header for path: {path}")
            response = Response(
                json.dumps({"error": "Missing or invalid token"}),
                status=401,
                mimetype='application/json'
            )
            return response(environ, start_response)

        token = auth_header.split(' ')[1]

        try:
            # Verify the token
            print(f"Verifying token with secret: {self.jwt_secret[:10]}...")
            payload = jwt.decode(token, self.jwt_secret, algorithms=['HS256'])
            user_id = payload.get('sub')
            role = payload.get('role', 'Unknown')

            # Log token payload for debugging
            print(f"Token payload: {payload}")
            print(f"User ID: {user_id}, Role: {role}")

            if not role or role == 'Unknown':
                logger.warning(f"Unknown role in token for path: {path}")
                response = Response(
                    json.dumps({"error": "Unknown Role", "message": "Your role ({}) is not recognized.".format(role)}),
                    status=403,
                    mimetype='application/json'
                )
                return response(environ, start_response)

            # Check if the route requires a specific role
            for route_prefix, roles in self.required_roles.items():
                if path.startswith(route_prefix):
                    print(f"Checking permissions for route {path} which requires roles {roles}")
                    print(f"User role: {role}")

                    # Special case for Super Admin - always allow access
                    if role == 'Super Admin':
                        print("Super Admin detected - granting access")
                        break

                    if role not in roles:
                        logger.warning(f"User with role {role} attempted to access {path} which requires roles {roles}")
                        response = Response(
                            json.dumps({"error": "Insufficient permissions"}),
                            status=403,
                            mimetype='application/json'
                        )
                        return response(environ, start_response)

            # Add user info to the request environment
            environ['user_id'] = user_id
            environ['user_role'] = role

            return self.app(environ, start_response)

        except jwt.ExpiredSignatureError:
            logger.warning(f"Expired token for path: {path}")
            response = Response(
                json.dumps({"error": "Token expired"}),
                status=401,
                mimetype='application/json'
            )
            return response(environ, start_response)

        except jwt.InvalidTokenError:
            logger.warning(f"Invalid token for path: {path}")
            response = Response(
                json.dumps({"error": "Invalid token"}),
                status=401,
                mimetype='application/json'
            )
            return response(environ, start_response)
