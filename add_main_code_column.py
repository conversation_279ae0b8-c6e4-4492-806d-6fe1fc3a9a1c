"""
<PERSON><PERSON><PERSON> to add the main_code column to the students table.
"""

import psycopg2
import sys

def add_main_code_column():
    """Add main_code column to students table."""
    try:
        # Connect to the student_db database
        conn = psycopg2.connect(
            dbname="student_db",
            user="postgres",
            password="postgres",
            host="localhost",
            port="5432"
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Check if the column already exists
        cursor.execute("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name='students' AND column_name='main_code'
        """)
        
        if cursor.fetchone():
            print("main_code column already exists in students table.")
            return True
        
        # Add the main_code column
        cursor.execute("""
            ALTER TABLE students
            ADD COLUMN main_code VARCHAR(50)
        """)
        
        print("main_code column added to students table successfully.")
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

if __name__ == "__main__":
    success = add_main_code_column()
    sys.exit(0 if success else 1)
