/**
 * Dashboard Layout Component
 * 
 * This component provides a common layout for all dashboard pages.
 * 
 * English: This component provides a common layout with header and sidebar for dashboards
 * Tanglish: Indha component dashboard pages-kku common layout-a provide pannum
 */

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const DashboardLayout = ({ children, title }) => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  /**
   * Handle logout
   * 
   * English: This function handles user logout
   * Tanglish: Indha function user logout-a handle pannum
   */
  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  
  /**
   * Toggle sidebar
   * 
   * English: This function toggles the sidebar visibility
   * Tanglish: Indha function sidebar visibility-a toggle pannum
   */
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  
  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className={`bg-gray-800 text-white w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:relative md:translate-x-0 transition duration-200 ease-in-out`}>
        <div className="flex items-center justify-between px-4">
          <h2 className="text-2xl font-bold">School MS</h2>
          <button onClick={toggleSidebar} className="md:hidden">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <nav>
          <a href="/dashboard" className="block py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700">
            Dashboard
          </a>
          
          <button 
            onClick={handleLogout}
            className="block w-full text-left py-2.5 px-4 rounded transition duration-200 hover:bg-gray-700 mt-6"
          >
            Logout
          </button>
        </nav>
      </div>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-md">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center">
              <button onClick={toggleSidebar} className="text-gray-500 focus:outline-none md:hidden">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
              </button>
              <h1 className="text-xl font-semibold text-gray-800 ml-4">{title}</h1>
            </div>
            
            <div className="flex items-center">
              <span className="text-gray-600 mr-4">
                Welcome, {currentUser ? currentUser.username : 'User'}
              </span>
              <button 
                onClick={handleLogout}
                className="btn btn-secondary"
              >
                Logout
              </button>
            </div>
          </div>
        </header>
        
        {/* Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;