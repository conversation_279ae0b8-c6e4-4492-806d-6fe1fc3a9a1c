"""
<PERSON><PERSON><PERSON> to add course and main_code columns to the users tables in auth_db and user_db.

This script adds the course and main_code columns to the users tables if they don't already exist.
"""

import os
import sys
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection(db_name):
    """Get a database connection."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    
    return conn

def add_columns_to_db(db_name):
    """Add course and main_code columns to the users table in the specified database."""
    try:
        conn = get_db_connection(db_name)
        cursor = conn.cursor()
        
        # Check if the columns already exist
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='users' AND column_name IN ('course', 'main_code')
        """)
        existing_columns = [col[0] for col in cursor.fetchall()]
        
        # Add course column if it doesn't exist
        if 'course' not in existing_columns:
            cursor.execute("""
                ALTER TABLE users
                ADD COLUMN course VARCHAR(50)
            """)
            print(f"Added 'course' column to users table in {db_name}")
        else:
            print(f"'course' column already exists in users table in {db_name}")
        
        # Add main_code column if it doesn't exist
        if 'main_code' not in existing_columns:
            cursor.execute("""
                ALTER TABLE users
                ADD COLUMN main_code VARCHAR(50)
            """)
            print(f"Added 'main_code' column to users table in {db_name}")
        else:
            print(f"'main_code' column already exists in users table in {db_name}")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"Error adding columns to {db_name}: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

def main():
    """Main function."""
    # Add columns to auth_db
    auth_success = add_columns_to_db('auth_db')
    
    # Add columns to user_db
    user_success = add_columns_to_db('user_db')
    
    if auth_success and user_success:
        print("Successfully added columns to both databases!")
        return 0
    else:
        print("Failed to add columns to one or both databases.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
