2025-05-13 15:15:52,809 - parent_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/parents/parents
2025-05-13 15:48:31,362 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 15:51:59,384 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-13 16:00:05,691 - parent_service.common.utils - ERROR - Error: Parent with this user_id already exists, Status: 400
2025-05-14 10:57:15,757 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:16,020 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:16,068 - parent_service.common.middleware - WARNING - User with role Pa<PERSON> attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:17,693 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,008 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,315 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:18,836 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:19,149 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 10:57:19,462 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:43,932 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:44,241 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:13:44,552 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:37,890 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:38,147 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:38,196 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:49,257 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
2025-05-14 11:34:49,879 - parent_service.common.middleware - WARNING - User with role Parent attempted to access /api/parents/parents/user/21 which requires roles ['Super Admin', 'Admin', 'Teacher']
