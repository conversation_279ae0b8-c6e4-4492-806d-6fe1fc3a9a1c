@echo off
echo Setting up the environment and running services...

echo Installing dependencies with specific versions...
pip install -r requirements.txt

echo Creating a new Python virtual environment...
python -m venv venv

echo Activating the virtual environment...
call venv\Scripts\activate

echo Installing dependencies in the virtual environment...
pip install -r requirements.txt

echo Starting services...
start cmd /k python -m auth_service.app
timeout /t 5
start cmd /k python -m user_service.app
timeout /t 5
start cmd /k python -m student_service.app
timeout /t 5
start cmd /k python -m course_service.app
timeout /t 5
start cmd /k python -m parent_service.app

echo All services started!
echo Auth Service: http://localhost:5000
echo User Service: http://localhost:5001
echo Student Service: http://localhost:5002
echo Course Service: http://localhost:5003
echo Parent Service: http://localhost:5004

echo To start the frontend, open a new terminal and run: cd frontend && npm run dev
