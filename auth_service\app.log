2025-05-19 11:24:40,991 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-19 11:24:40,991 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-19 11:24:40,993 - werkzeug - INFO -  * Restarting with stat
2025-05-19 11:24:41,742 - werkzeug - WARNING -  * Debugger is active!
2025-05-19 11:24:41,744 - werkzeug - INFO -  * Debugger PIN: 154-756-629
2025-05-19 11:25:49,449 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-19 11:25:49,450 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:25:49] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
2025-05-19 11:26:01,953 - auth_service.common.utils - ERROR - Error: Invalid username or password, Status: 401
2025-05-19 11:26:01,953 - werkzeug - INFO - 127.0.0.1 - - [19/May/2025 11:26:01] "[31m[1mPOST /api/auth/login HTTP/1.1[0m" 401 -
