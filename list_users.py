"""
<PERSON><PERSON>t to list all users in auth_db and user_db.

This script lists all users in the auth_db and user_db databases.
"""

import os
import sys
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection(db_name):
    """Get a database connection."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    
    return conn

def list_users(db_name):
    """List all users in the specified database."""
    try:
        conn = get_db_connection(db_name)
        cursor = conn.cursor()
        
        # Get all users
        cursor.execute("""
            SELECT id, username, email, role, is_admin, course, main_code
            FROM users
        """)
        
        users = cursor.fetchall()
        
        if users:
            print(f"\nUsers in {db_name}:")
            print("-" * 80)
            print(f"{'ID':<5} {'Username':<15} {'Email':<25} {'Role':<15} {'Is Admin':<10} {'Course':<10} {'Main Code':<10}")
            print("-" * 80)
            
            for user in users:
                id, username, email, role, is_admin, course, main_code = user
                print(f"{id:<5} {username:<15} {email:<25} {role:<15} {is_admin:<10} {course or 'None':<10} {main_code or 'None':<10}")
        else:
            print(f"\nNo users found in {db_name}")
        
        return True
        
    except Exception as e:
        print(f"Error listing users in {db_name}: {e}")
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

def main():
    """Main function."""
    # List users in auth_db
    auth_success = list_users('auth_db')
    
    # List users in user_db
    user_success = list_users('user_db')
    
    if auth_success and user_success:
        return 0
    else:
        return 1

if __name__ == '__main__':
    sys.exit(main())
