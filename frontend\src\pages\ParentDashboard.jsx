/**
 * Parent Dashboard Page
 * 
 * This page shows the dashboard for Parent users.
 * 
 * English: This page shows the Parent dashboard with their children's information
 * Tanglish: Indha page Parent-kku dashboard-a display pannum, avanga children-oda information-oda
 */

import { useState, useEffect } from 'react';
import DashboardLayout from '../components/DashboardLayout';
import { useAuth } from '../contexts/AuthContext';
import parentService from '../services/parentService';
import studentService from '../services/studentService';
import courseService from '../services/courseService';

const ParentDashboard = () => {
  // Get current user from auth context
  const { currentUser } = useAuth();
  
  // State for parent
  const [parent, setParent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // State for children
  const [children, setChildren] = useState([]);
  const [loadingChildren, setLoadingChildren] = useState(false);
  
  // State for selected child
  const [selectedChild, setSelectedChild] = useState(null);
  
  // State for child's courses
  const [childCourses, setChildCourses] = useState([]);
  const [loadingChildCourses, setLoadingChildCourses] = useState(false);
  
  /**
   * Load parent data from API
   * 
   * English: This function loads the parent data from the API
   * Tanglish: Indha function API-la irundhu parent data-va load pannum
   */
  const loadParent = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Get the parent by user ID
      const data = await parentService.getParentByUserId(currentUser.id);
      setParent(data.parent);
      
      // Then get the parent's children
      if (data.parent) {
        await loadChildren(data.parent.id);
      }
    } catch (error) {
      setError(error.error || 'Failed to load parent data');
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Load children from API
   * 
   * @param {number} parentId - ID of the parent
   * 
   * English: This function loads the parent's children from the API
   * Tanglish: Indha function API-la irundhu parent-oda children-a load pannum
   */
  const loadChildren = async (parentId) => {
    try {
      setLoadingChildren(true);
      
      const data = await studentService.getParentStudents(parentId);
      setChildren(data.students || []);
    } catch (error) {
      console.error('Failed to load children:', error);
    } finally {
      setLoadingChildren(false);
    }
  };
  
  /**
   * Load child's courses from API
   * 
   * @param {number} studentId - ID of the student
   * 
   * English: This function loads the child's courses from the API
   * Tanglish: Indha function API-la irundhu child-oda courses-a load pannum
   */
  const loadChildCourses = async (studentId) => {
    try {
      setLoadingChildCourses(true);
      
      const data = await courseService.getStudentCourses(studentId);
      setChildCourses(data.courses || []);
    } catch (error) {
      console.error('Failed to load courses:', error);
    } finally {
      setLoadingChildCourses(false);
    }
  };
  
  /**
   * Handle child selection
   * 
   * @param {Object} child - Selected child
   * 
   * English: This function handles child selection to view their details
   * Tanglish: Indha function child selection-a handle panni avanga details-a view panna
   */
  const handleChildSelect = (child) => {
    setSelectedChild(child.student);
    loadChildCourses(child.student_id);
  };
  
  // Load data when component mounts
  useEffect(() => {
    loadParent();
  }, []);
  
  return (
    <DashboardLayout title="Parent Dashboard">
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-lg">Loading parent data...</p>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : parent ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <div className="card mb-6">
              <h2 className="text-xl font-semibold mb-4">Parent Profile</h2>
              
              <div>
                <p className="mb-2"><strong>Name:</strong> {parent.first_name} {parent.last_name}</p>
                <p className="mb-2"><strong>Occupation:</strong> {parent.occupation || 'Not specified'}</p>
                <p className="mb-2"><strong>Address:</strong> {parent.address || 'Not specified'}</p>
                <p className="mb-2"><strong>Phone:</strong> {parent.phone || 'Not specified'}</p>
              </div>
            </div>
            
            <div className="card">
              <h2 className="text-xl font-semibold mb-4">My Children</h2>
              
              {loadingChildren ? (
                <p>Loading children...</p>
              ) : children.length === 0 ? (
                <p>No children found.</p>
              ) : (
                <div className="space-y-2">
                  {children.map((child) => (
                    <div 
                      key={child.id} 
                      className={`p-3 rounded cursor-pointer ${selectedChild && selectedChild.id === child.student.id ? 'bg-blue-100 border-blue-500 border' : 'bg-gray-100 hover:bg-gray-200'}`}
                      onClick={() => handleChildSelect(child)}
                    >
                      <p className="font-semibold">{child.student.first_name} {child.student.last_name}</p>
                      <p className="text-sm text-gray-600">Relationship: {child.relationship || 'Not specified'}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <div className="md:col-span-2">
            {selectedChild ? (
              <div>
                <div className="card mb-6">
                  <h2 className="text-xl font-semibold mb-4">Child Details</h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="mb-2"><strong>Name:</strong> {selectedChild.first_name} {selectedChild.last_name}</p>
                      <p className="mb-2"><strong>Date of Birth:</strong> {selectedChild.date_of_birth || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="mb-2"><strong>Address:</strong> {selectedChild.address || 'Not specified'}</p>
                      <p className="mb-2"><strong>Phone:</strong> {selectedChild.phone || 'Not specified'}</p>
                    </div>
                  </div>
                </div>
                
                <div className="card">
                  <h2 className="text-xl font-semibold mb-4">Child's Courses</h2>
                  
                  {loadingChildCourses ? (
                    <p>Loading courses...</p>
                  ) : childCourses.length === 0 ? (
                    <p>Your child is not enrolled in any courses yet.</p>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {childCourses.map((courseMapping) => (
                        <div key={courseMapping.id} className="bg-white p-4 rounded shadow">
                          <h3 className="text-lg font-semibold">{courseMapping.course.name}</h3>
                          <p className="text-gray-600">{courseMapping.course.description || 'No description'}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="card flex items-center justify-center h-64">
                <p className="text-gray-500">Select a child to view details</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="alert alert-danger" role="alert">
          No parent data available.
        </div>
      )}
    </DashboardLayout>
  );
};

export default ParentDashboard;
