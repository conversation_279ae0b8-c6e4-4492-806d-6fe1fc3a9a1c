"""
Models for the Parent Service.

This module defines the database models for the Parent Service.

English: This file defines the database tables for the Parent Service
Tanglish: Indha file Parent Service-kku database tables-a define pannum
"""

from datetime import datetime
from parent_service.common.db_config import db

class Parent(db.Model):
    """
    Parent model.

    English: This model stores parent information
    Tanglish: Indha model parent information-a store pannum
    """
    __tablename__ = 'parents'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, unique=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    occupation = db.Column(db.String(100), nullable=True)
    address = db.Column(db.Text, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    # main_code column is defined in the model but not in the database
    # main_code = db.Column(db.String(50), nullable=True)  # For school-wise separation
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, user_id, first_name, last_name, occupation=None, address=None, phone=None, main_code=None):
        """
        Initialize a new Parent.

        Args:
            user_id: ID of the user associated with this parent
            first_name: Parent's first name
            last_name: Parent's last name
            occupation: Parent's occupation (optional)
            address: Parent's address (optional)
            phone: Parent's phone number (optional)
            main_code: School code for school-wise separation (optional) - Not used currently

        English: This function creates a new parent with the given information
        Tanglish: Indha function kudukkapatta information-oda puthusa oru parent-a create pannum
        """
        self.user_id = user_id
        self.first_name = first_name
        self.last_name = last_name
        self.occupation = occupation
        self.address = address
        self.phone = phone
        # Ignore main_code since it's not in the database
        # self.main_code = main_code

    def to_dict(self):
        """
        Convert the parent to a dictionary.

        Returns:
            Dictionary representation of the parent

        English: This function converts the parent to a dictionary
        Tanglish: Indha function parent-a dictionary-a convert pannum
        """
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'occupation': self.occupation,
            'address': self.address,
            'phone': self.phone,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # Add a fake main_code for compatibility with the frontend
        result['main_code'] = "RVM677"  # Use the superadmin's main_code

        return result
