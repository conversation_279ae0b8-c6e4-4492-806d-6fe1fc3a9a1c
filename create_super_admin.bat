@echo off
echo Creating Super Admin User...

set /p username=Enter username (e.g., superadmin): 
set /p password=Enter password: 
set /p email=Enter email (e.g., <EMAIL>): 

echo.
echo Creating Super Admin user with:
echo Username: %username%
echo Password: %password%
echo Email: %email%
echo.

python register_super_admin.py --username %username% --password %password% --email %email%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Super Admin user created successfully!
    echo You can now log in with these credentials.
) else (
    echo.
    echo Failed to create Super Admin user. Please check the error message above.
)

pause
